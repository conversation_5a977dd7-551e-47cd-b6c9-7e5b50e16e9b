{%- render 'section-spacing-collapsing' -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    --benefits-cards-gap: var(--spacing-2);
    --benefits-card-border-color: rgb(var(--text-primary));
    --benefits-card-background: rgb(var(--background-primary));
    --benefits-card-background-hover: rgb(var(--text-primary) / 0.05);
    --benefits-card-min-height: 280px;
  }

  @media screen and (min-width: 700px) {
    #shopify-section-{{ section.id }} {
      --benefits-cards-gap: var(--spacing-3);
      --benefits-card-min-height: 320px;
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
      --benefits-cards-gap: var(--spacing-4);
      --benefits-card-min-height: 360px;
    }
  }

  .benefits-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--benefits-cards-gap);
    width: 100%;
    max-width: 100%;
  }

  @media screen and (min-width: 700px) {
    .benefits-cards {
      grid-template-columns: repeat(2, 1fr);
      max-width: 1000px;
      margin: 0 auto;
    }
  }

  .benefits-card {
    position: relative;
    min-height: var(--benefits-card-min-height);
    border: 2px solid var(--benefits-card-border-color);
    background: var(--benefits-card-background);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    perspective: 1000px;
    border-radius: var(--spacing-4);
  }

  .benefits-card__inner {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: var(--benefits-card-min-height);
    transition: transform 0.6s;
    transform-style: preserve-3d;
  }

  .benefits-card.is-flipped .benefits-card__inner {
    transform: rotateY(180deg);
  }

  .benefits-card__front,
  .benefits-card__back {
    position: absolute;
    width: 100%;
    height: 100%;
    min-height: var(--benefits-card-min-height);
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-6);
  }

  .benefits-card__back {
    transform: rotateY(180deg);
    background: var(--benefits-card-background-hover);
  }

  .benefits-card__number {
    font-family: var(--text-font-family);
    font-size: 4rem;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(var(--text-primary));
  }

  @media screen and (min-width: 700px) {
    .benefits-card__number {
      font-size: 5rem;
    }
  }

  @media screen and (min-width: 1000px) {
    .benefits-card__number {
      font-size: 6rem;
    }
  }

  .benefits-card__description {
    font-family: var(--heading-font-family);
    font-size: var(--text-xl);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgb(var(--text-primary));
    margin-top: auto;
  }

  @media screen and (min-width: 700px) {
    .benefits-card__description {
      font-size: var(--text-lg);
    }
  }

  .benefits-card__back {
    background: rgb(var(--text-primary));
    color: rgb(var(--background-primary));
  }

  .benefits-card__back-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  .benefits-card__back-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    overflow: hidden;
  }

  .benefits-card__back-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .benefits-card__back-text {
    position: absolute;
    top: 50%;
    left: var(--spacing-4);
    right: 80px; /* Leave space for minus icon */
    bottom: var(--spacing-4);
    font-family: var(--heading-font-family);
    font-size: var(--text-base);
    line-height: 1.3;
    color: rgb(var(--background-primary));
    text-align: left;
    z-index: 2;
    display: flex;
    align-items: flex-start;
    padding-top: var(--spacing-4);
  }

  @media screen and (min-width: 700px) {
    .benefits-card__back-text {
      font-size: var(--text-lg);
      left: var(--spacing-6);
      right: 90px; /* Leave more space for larger minus icon */
      bottom: var(--spacing-6);
      padding-top: var(--spacing-6);
    }
  }

  .benefits-card__plus-icon {
    position: absolute;
    bottom: var(--spacing-3);
    right: var(--spacing-3);
    width: 32px;
    height: 32px;
    background: rgb(var(--text-primary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(var(--background-primary));
    font-size: 16px;
    font-weight: bold;
    z-index: 2;
    line-height: 1;
  }

  @media screen and (min-width: 700px) {
    .benefits-card__plus-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
      bottom: var(--spacing-4);
      right: var(--spacing-4);
    }
  }

  .benefits-card__minus-icon {
    position: absolute;
    bottom: var(--spacing-3);
    right: var(--spacing-3);
    width: 32px;
    height: 32px;
    background: rgb(var(--background-primary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(var(--text-primary));
    font-size: 16px;
    font-weight: bold;
    z-index: 3;
    line-height: 1;
  }

  @media screen and (min-width: 700px) {
    .benefits-card__minus-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
      bottom: var(--spacing-4);
      right: var(--spacing-4);
    }
  }

  /* Hover effects */
  @media screen and (pointer: fine) {
    .benefits-card:hover {
      background: var(--benefits-card-background-hover);
    }
  }

  /* Touch device support */
  @media screen and (pointer: coarse) {
    .benefits-card {
      cursor: default;
    }
  }
</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
LIQUID
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<div {% render 'section-properties' %}>
  <div class="section-stack">
    {%- if section.settings.title != blank -%}
      <div class="section-stack__intro">
        <h2 class="h2" style="font-family: var(--heading-font-family); text-align: left;">
          {%- render 'styled-text', content: section.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient -%}
        </h2>
      </div>
    {%- endif -%}

    <div class="section-stack__main">
      <div class="benefits-cards">
        {%- for block in section.blocks -%}
          <div class="benefits-card"
               id="benefits-card-{{ block.id }}"
               data-card-id="{{ block.id }}"
               {{ block.shopify_attributes }}>
            <div class="benefits-card__inner">
              {%- comment -%} Front of card {%- endcomment -%}
              <div class="benefits-card__front">
                <div class="benefits-card__number">
                  {{ block.settings.number }}{{ block.settings.unit }}
                </div>
                <div class="benefits-card__description">
                  {{ block.settings.description | escape }}
                </div>
                <div class="benefits-card__plus-icon">+</div>
              </div>

              {%- comment -%} Back of card {%- endcomment -%}
              <div class="benefits-card__back">
                <div class="benefits-card__back-content">
                  <div class="benefits-card__back-image">
                    {%- if block.settings.back_image != blank -%}
                      {%- assign image_loading = 'lazy' -%}
                      {%- if section.index <= 2 -%}
                        {%- assign image_loading = 'eager' -%}
                      {%- endif -%}

                      {{- block.settings.back_image | image_url: width: 600 | image_tag:
                          loading: image_loading,
                          sizes: '(max-width: 699px) 100vw, 50vw',
                          widths: '300,400,500,600,800,1000' -}}
                    {%- else -%}
                      <img src="https://cdn.shopify.com/s/files/1/0878/9170/6174/files/magnesium.png?v=1737173816" alt="Temporary background" loading="lazy">
                    {%- endif -%}
                  </div>

                  {%- if block.settings.back_text != blank -%}
                    <div class="benefits-card__back-text">
                      {{ block.settings.back_text }}
                    </div>
                  {%- endif -%}

                  <div class="benefits-card__minus-icon">−</div>
                </div>
              </div>
            </div>
          </div>
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
JAVASCRIPT
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('#shopify-section-{{ section.id }} .benefits-card');

    cards.forEach(card => {
      let isFlipped = false;

      // Handle click/touch events
      card.addEventListener('click', function(e) {
        e.preventDefault();
        toggleCard();
      });

      // Handle hover events for desktop
      if (window.matchMedia('(pointer: fine)').matches) {
        card.addEventListener('mouseenter', function() {
          if (!isFlipped) {
            toggleCard();
          }
        });

        card.addEventListener('mouseleave', function() {
          if (isFlipped) {
            toggleCard();
          }
        });
      }

      function toggleCard() {
        isFlipped = !isFlipped;
        card.classList.toggle('is-flipped', isFlipped);
      }
    });
  });
</script>

{% schema %}
{
  "name": "Benefits Cards",
  "class": "shopify-section--benefits-cards",
  "tag": "section",
  "disabled_on": {
    "groups": ["header"]
  },
  "max_blocks": 4,
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Benefits in Every Sip"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    }
  ],
  "blocks": [
    {
      "type": "benefit_card",
      "name": "Benefit Card",
      "settings": [
        {
          "type": "text",
          "id": "number",
          "label": "Number",
          "default": "75",
          "info": "The main number displayed on the card"
        },
        {
          "type": "text",
          "id": "unit",
          "label": "Unit",
          "default": "%",
          "info": "Unit symbol (%, mg, etc.)"
        },
        {
          "type": "text",
          "id": "description",
          "label": "Description",
          "default": "Magnesium Deficiency",
          "info": "Text displayed at the bottom of the card"
        },
        {
          "type": "header",
          "content": "Card Back Content"
        },
        {
          "type": "image_picker",
          "id": "back_image",
          "label": "Back Image",
          "info": "Image shown when card is flipped"
        },
        {
          "type": "richtext",
          "id": "back_text",
          "label": "Back Text",
          "default": "<p>OSLYTES delivers 300mg of magnesium, covering 71% of the daily recommended intake</p>",
          "info": "Text displayed on the back of the card"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Benefits Cards",
      "blocks": [
        {
          "type": "benefit_card",
          "settings": {
            "number": "75",
            "unit": "%",
            "description": "Magnesium Deficiency",
            "back_text": "<p>OSLYTES delivers 300mg of magnesium, covering 71% of the daily recommended intake</p>"
          }
        },
        {
          "type": "benefit_card",
          "settings": {
            "number": "95",
            "unit": "%",
            "description": "Enzymes Need Magnesium",
            "back_text": "<p>Over 300 enzymatic reactions in your body require magnesium to function properly</p>"
          }
        },
        {
          "type": "benefit_card",
          "settings": {
            "number": "80",
            "unit": "%",
            "description": "Magnesium Absorption",
            "back_text": "<p>Our chelated magnesium forms provide superior bioavailability and absorption</p>"
          }
        },
        {
          "type": "benefit_card",
          "settings": {
            "number": "100",
            "unit": "%",
            "description": "Natural Ingredients",
            "back_text": "<p>Made with only the highest quality, natural ingredients for optimal health benefits</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
