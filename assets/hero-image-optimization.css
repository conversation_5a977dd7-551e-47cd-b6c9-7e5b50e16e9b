/* Hero Image Optimization CSS
 * Optimizations specifically for the hero section images to improve loading performance
 */

/* Ensure hero images are prioritized and render quickly */
.mobile-hero-image,
.images-scrolling-desktop__media-wrapper > img:first-child {
  /* Prevent layout shifts during image loading */
  aspect-ratio: attr(width) / attr(height);

  /* Optimize rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* Ensure proper sizing */
  width: 100%;
  height: auto;

  /* Prevent flickering during load */
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

/* Loading state for hero images */
.mobile-hero-image[loading="eager"],
.images-scrolling-desktop__media-wrapper > img:first-child[loading="eager"] {
  /* Ensure these images are rendered immediately */
  content-visibility: auto;
  contain-intrinsic-size: 800px 600px;
}

/* Optimize the hero section container for faster rendering */
.images-scrolling-mobile__item:first-child,
.images-scrolling-desktop__media-wrapper > :first-child {
  /* Promote to its own layer for faster compositing */
  will-change: transform;
  transform: translateZ(0);
}

/* Prevent cumulative layout shift in hero section */
.images-scrolling-mobile__item:first-child .mobile-hero-image {
  /* Reserve space to prevent layout shifts */
  min-height: 350px;
}

@media screen and (min-width: 741px) {
  .images-scrolling-mobile__item:first-child .mobile-hero-image {
    min-height: 450px;
  }

  /* Ensure desktop hero images are properly sized */
  .images-scrolling-desktop__media-wrapper > img:first-child {
    min-height: 500px;
    max-height: 80vh;
    object-fit: contain;
  }
}

/* Optimize for Core Web Vitals */
.images-scrolling-desktop__media-wrapper > img:first-child {
  /* Ensure the first image in desktop view loads immediately */
  loading: eager !important;
  fetchpriority: high !important;
}

.mobile-hero-image:first-of-type {
  /* Ensure the first mobile hero image loads immediately */
  loading: eager !important;
  fetchpriority: high !important;
}

/* Additional hero image sizing improvements */
@media screen and (max-width: 740px) {
  .mobile-hero-image {
    /* Make mobile images larger and more prominent */
    width: 100% !important;
    height: auto !important;
    max-width: none !important;
    object-fit: contain !important;
  }
}

@media screen and (min-width: 741px) {
  .images-scrolling-desktop__media-wrapper {
    /* Ensure the media wrapper takes full advantage of its space */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .images-scrolling-desktop__media-wrapper > img {
    /* Make desktop images larger and more prominent */
    width: 100% !important;
    height: auto !important;
    max-width: none !important;
    object-fit: contain !important;
  }
}
